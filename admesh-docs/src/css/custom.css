/**
 * AdMesh Documentation - Modern Design System
 * Inspired by <PERSON>e, Vercel, and Linear documentation
 */

/* Import Inter font for modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Modern Design System Variables */
:root {
  /* Brand Colors - Professional Purple/Blue Gradient */
  --ifm-color-primary: #6366f1;
  --ifm-color-primary-dark: #4f46e5;
  --ifm-color-primary-darker: #4338ca;
  --ifm-color-primary-darkest: #3730a3;
  --ifm-color-primary-light: #818cf8;
  --ifm-color-primary-lighter: #a5b4fc;
  --ifm-color-primary-lightest: #c7d2fe;

  /* Semantic Colors */
  --ifm-color-success: #10b981;
  --ifm-color-warning: #f59e0b;
  --ifm-color-danger: #ef4444;
  --ifm-color-info: #3b82f6;

  /* Typography */
  --ifm-font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --ifm-font-family-monospace: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --ifm-font-size-base: 16px;
  --ifm-line-height-base: 1.6;
  --ifm-font-weight-light: 300;
  --ifm-font-weight-normal: 400;
  --ifm-font-weight-semibold: 500;
  --ifm-font-weight-bold: 600;

  /* Spacing */
  --ifm-spacing-horizontal: 1.5rem;
  --ifm-spacing-vertical: 1.5rem;
  --ifm-container-width: 1200px;
  --ifm-container-width-xl: 1400px;

  /* Borders and Radius */
  --ifm-border-radius: 8px;
  --ifm-border-width: 1px;
  --ifm-border-color: #e2e8f0;

  /* Shadows */
  --ifm-box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ifm-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --ifm-box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ifm-box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Code */
  --ifm-code-font-size: 0.875rem;
  --ifm-code-background: #f8fafc;
  --ifm-code-border-radius: 6px;
  --ifm-code-padding-horizontal: 0.5rem;
  --ifm-code-padding-vertical: 0.25rem;
  --docusaurus-highlighted-code-line-bg: rgba(99, 102, 241, 0.1);

  /* Background Colors */
  --ifm-background-color: #ffffff;
  --ifm-background-surface-color: #f8fafc;
  --ifm-color-emphasis-0: #f8fafc;
  --ifm-color-emphasis-100: #f1f5f9;
  --ifm-color-emphasis-200: #e2e8f0;
  --ifm-color-emphasis-300: #cbd5e1;

  /* Text Colors */
  --ifm-color-content: #0f172a;
  --ifm-color-content-secondary: #475569;
  --ifm-heading-color: #0f172a;
}

/* Dark Mode Variables */
[data-theme='dark'] {
  /* Brand Colors - Adjusted for dark mode */
  --ifm-color-primary: #818cf8;
  --ifm-color-primary-dark: #6366f1;
  --ifm-color-primary-darker: #4f46e5;
  --ifm-color-primary-darkest: #4338ca;
  --ifm-color-primary-light: #a5b4fc;
  --ifm-color-primary-lighter: #c7d2fe;
  --ifm-color-primary-lightest: #e0e7ff;

  /* Dark Mode Backgrounds */
  --ifm-background-color: #0f172a;
  --ifm-background-surface-color: #1e293b;
  --ifm-color-emphasis-0: #1e293b;
  --ifm-color-emphasis-100: #334155;
  --ifm-color-emphasis-200: #475569;
  --ifm-color-emphasis-300: #64748b;

  /* Dark Mode Text */
  --ifm-color-content: #f1f5f9;
  --ifm-color-content-secondary: #cbd5e1;
  --ifm-heading-color: #f8fafc;

  /* Dark Mode Code */
  --ifm-code-background: #1e293b;
  --ifm-border-color: #334155;
  --docusaurus-highlighted-code-line-bg: rgba(129, 140, 248, 0.15);
}

/* ===== MODERN LAYOUT COMPONENTS ===== */

/* Navbar Enhancements */
.navbar {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

[data-theme='dark'] .navbar {
  background: rgba(15, 23, 42, 0.8);
}

.navbar__brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--ifm-color-primary);
  transition: all 0.2s ease;
}

.navbar__brand:hover {
  color: var(--ifm-color-primary-dark);
  text-decoration: none;
}

.navbar__item {
  font-weight: 500;
  transition: all 0.2s ease;
}

.navbar__link {
  border-radius: 6px;
  padding: 0.5rem 0.75rem !important;
  transition: all 0.2s ease;
}

.navbar__link:hover {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-primary);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05) 0%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(139, 92, 246, 0.05) 100%);
  border-bottom: 1px solid var(--ifm-border-color);
  padding: 4rem 0;
}

.hero__title {
  background: linear-gradient(135deg, #6366f1 0%, #3b82f6 50%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 3.5rem;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.hero__subtitle {
  font-size: 1.25rem;
  color: var(--ifm-color-content-secondary);
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

/* Modern Buttons */
.button {
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: none;
  box-shadow: var(--ifm-box-shadow-sm);
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: var(--ifm-box-shadow-md);
}

.button--primary {
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-primary-dark) 100%);
  color: white;
}

.button--primary:hover {
  background: linear-gradient(135deg, var(--ifm-color-primary-dark) 0%, var(--ifm-color-primary-darker) 100%);
  color: white;
}

.button--secondary {
  background: var(--ifm-background-surface-color);
  color: var(--ifm-color-content);
  border: 1px solid var(--ifm-border-color);
}

.button--secondary:hover {
  background: var(--ifm-color-emphasis-100);
  border-color: var(--ifm-color-primary);
  color: var(--ifm-color-primary);
}

/* ===== CODE BLOCKS & SYNTAX HIGHLIGHTING ===== */

/* Modern Code Blocks */
.prism-code {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
  background: var(--ifm-code-background) !important;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
}

.code-block-with-title {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
}

.code-block-title {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-content-secondary);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--ifm-border-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Inline Code */
code {
  background: var(--ifm-code-background);
  color: var(--ifm-color-primary-dark);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: var(--ifm-code-border-radius);
  padding: var(--ifm-code-padding-vertical) var(--ifm-code-padding-horizontal);
  font-family: var(--ifm-font-family-monospace);
  font-size: var(--ifm-code-font-size);
  font-weight: 500;
}

/* Copy Button Improvements */
.clean-btn {
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 0.5rem;
  background: var(--ifm-color-emphasis-100);
  border: 1px solid var(--ifm-border-color);
}

.clean-btn:hover {
  transform: translateY(-1px);
  background: var(--ifm-color-primary);
  color: white;
  border-color: var(--ifm-color-primary);
}

/* Language Labels */
.prism-code .token-line::before {
  content: attr(data-language);
  position: absolute;
  top: 0.75rem;
  right: 1rem;
  font-size: 0.75rem;
  color: var(--ifm-color-content-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

/* ===== CONTENT LAYOUT & CARDS ===== */

/* Modern Cards */
.card {
  background: var(--ifm-background-color);
  border: 1px solid var(--ifm-border-color);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
  height: 100%;
}

.card:hover {
  box-shadow: var(--ifm-box-shadow-md);
  transform: translateY(-2px);
  border-color: var(--ifm-color-primary);
}

.card__header {
  margin-bottom: 1rem;
}

.card__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ifm-heading-color);
  margin-bottom: 0.5rem;
}

.card__body {
  color: var(--ifm-color-content-secondary);
  line-height: 1.6;
}

/* Modern Badges */
.badge {
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
}

.badge--primary {
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-primary-dark) 100%);
  color: white;
}

.badge--success {
  background: linear-gradient(135deg, var(--ifm-color-success) 0%, #059669 100%);
  color: white;
}

.badge--warning {
  background: linear-gradient(135deg, var(--ifm-color-warning) 0%, #d97706 100%);
  color: white;
}

.badge--danger {
  background: linear-gradient(135deg, var(--ifm-color-danger) 0%, #dc2626 100%);
  color: white;
}

.badge--info {
  background: linear-gradient(135deg, var(--ifm-color-info) 0%, #2563eb 100%);
  color: white;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.content-grid--2 {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.content-grid--3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* ===== API DOCUMENTATION COMPONENTS ===== */

/* Modern API Endpoint Styling */
.api-endpoint {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.api-endpoint:hover {
  transform: translateY(-1px);
  box-shadow: var(--ifm-box-shadow-md);
}

.api-endpoint--get {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  color: var(--ifm-color-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.api-endpoint--post {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
  color: var(--ifm-color-info);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.api-endpoint--put {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  color: var(--ifm-color-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.api-endpoint--delete {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  color: var(--ifm-color-danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* API Method Labels */
.api-method {
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: currentColor;
  color: white;
  margin-right: 0.5rem;
}

/* ===== TABLES & DATA DISPLAY ===== */

/* Modern Tables */
.table-wrapper {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  background: var(--ifm-background-color);
}

th {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-heading-color);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--ifm-border-color);
  text-align: left;
}

td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--ifm-border-color);
  color: var(--ifm-color-content);
  vertical-align: top;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background: var(--ifm-color-emphasis-0);
}

/* SDK Comparison Table */
.sdk-comparison {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
}

.sdk-comparison th,
.sdk-comparison td {
  padding: 1rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--ifm-border-color);
}

.sdk-comparison th {
  background: var(--ifm-color-emphasis-100);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--ifm-heading-color);
}

.sdk-comparison tr:hover {
  background: var(--ifm-color-emphasis-0);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.status-indicator--available {
  color: var(--ifm-color-success);
}

.status-indicator--coming-soon {
  color: var(--ifm-color-warning);
}

.status-indicator--not-available {
  color: var(--ifm-color-content-secondary);
}

/* ===== ADMONITIONS & FEATURE BOXES ===== */

/* Modern Admonitions */
.admonition {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
  overflow: hidden;
  background: var(--ifm-background-color);
}

.admonition-heading {
  background: var(--ifm-color-emphasis-100);
  padding: 1rem 1.5rem;
  margin: 0;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--ifm-border-color);
}

.admonition-content {
  padding: 1.5rem;
}

/* Admonition Types */
.admonition-note {
  border-left: 4px solid var(--ifm-color-info);
}

.admonition-note .admonition-heading {
  color: var(--ifm-color-info);
  background: rgba(59, 130, 246, 0.1);
}

.admonition-tip {
  border-left: 4px solid var(--ifm-color-success);
}

.admonition-tip .admonition-heading {
  color: var(--ifm-color-success);
  background: rgba(16, 185, 129, 0.1);
}

.admonition-warning {
  border-left: 4px solid var(--ifm-color-warning);
}

.admonition-warning .admonition-heading {
  color: var(--ifm-color-warning);
  background: rgba(245, 158, 11, 0.1);
}

.admonition-danger {
  border-left: 4px solid var(--ifm-color-danger);
}

.admonition-danger .admonition-heading {
  color: var(--ifm-color-danger);
  background: rgba(239, 68, 68, 0.1);
}

/* Feature Boxes */
.feature-box {
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid var(--ifm-border-color);
  background: var(--ifm-background-color);
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.feature-box:hover {
  box-shadow: var(--ifm-box-shadow-md);
  transform: translateY(-2px);
}

.feature-box--primary {
  border-left: 4px solid var(--ifm-color-primary);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(99, 102, 241, 0.02) 100%);
}

.feature-box--success {
  border-left: 4px solid var(--ifm-color-success);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.feature-box--warning {
  border-left: 4px solid var(--ifm-color-warning);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.feature-box--danger {
  border-left: 4px solid var(--ifm-color-danger);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
}

/* ===== INTERACTIVE COMPONENTS ===== */

/* Modern Tabs */
.tabs {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.tabs__nav {
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
  padding: 0;
  margin: 0;
}

.tabs__item {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tabs__item:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.tabs__item--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.tabs__content {
  padding: 1.5rem;
  background: var(--ifm-background-color);
}

/* Interactive Code Examples */
.code-example {
  position: relative;
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.code-example__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
  margin: 0;
}

.code-example__tab {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.code-example__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.code-example__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--ifm-color-emphasis-300);
  border-radius: 50%;
  border-top-color: var(--ifm-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--ifm-color-emphasis-100) 25%, var(--ifm-color-emphasis-200) 50%, var(--ifm-color-emphasis-100) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 6px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Optimizations */
@media (max-width: 996px) {
  .hero {
    padding: 3rem 0;
  }

  .hero__title {
    font-size: 2.5rem;
  }

  .hero__subtitle {
    font-size: 1.125rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .content-grid--2,
  .content-grid--3 {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 1.25rem;
  }

  .feature-box {
    padding: 1.5rem;
  }

  .tabs__item,
  .code-example__tab {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .hero__title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .hero__subtitle {
    font-size: 1rem;
  }

  .api-endpoint {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    gap: 0.25rem;
  }

  .sdk-comparison th,
  .sdk-comparison td {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .feature-box {
    padding: 1.25rem;
  }

  .card {
    padding: 1rem;
  }

  .button {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .navbar__item {
    margin: 0 0.25rem;
  }

  .tabs__item,
  .code-example__tab {
    padding: 0.625rem 0.875rem;
    font-size: 0.75rem;
  }

  th, td {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 2rem 0;
  }

  .hero__title {
    font-size: 1.75rem;
  }

  .hero__subtitle {
    font-size: 0.9rem;
  }

  .card,
  .feature-box {
    padding: 1rem;
  }

  .api-endpoint {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* ===== DARK MODE ENHANCEMENTS ===== */

[data-theme='dark'] .hero {
  background: linear-gradient(135deg,
    rgba(129, 140, 248, 0.1) 0%,
    rgba(96, 165, 250, 0.1) 50%,
    rgba(167, 139, 250, 0.1) 100%);
}

[data-theme='dark'] .card:hover {
  border-color: var(--ifm-color-primary-light);
}

[data-theme='dark'] .feature-box--primary {
  background: linear-gradient(135deg, rgba(129, 140, 248, 0.1) 0%, rgba(129, 140, 248, 0.05) 100%);
}

[data-theme='dark'] .feature-box--success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
}

[data-theme='dark'] .feature-box--warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
}

[data-theme='dark'] .feature-box--danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

[data-theme='dark'] .admonition-note .admonition-heading {
  background: rgba(96, 165, 250, 0.15);
}

[data-theme='dark'] .admonition-tip .admonition-heading {
  background: rgba(16, 185, 129, 0.15);
}

[data-theme='dark'] .admonition-warning .admonition-heading {
  background: rgba(245, 158, 11, 0.15);
}

[data-theme='dark'] .admonition-danger .admonition-heading {
  background: rgba(239, 68, 68, 0.15);
}

/* ===== MODERN UTILITIES ===== */

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--ifm-color-primary);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--ifm-color-primary);
  color: white;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ifm-color-emphasis-100);
}

::-webkit-scrollbar-thumb {
  background: var(--ifm-color-emphasis-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ifm-color-primary);
}

/* Modern Link Styling */
a {
  color: var(--ifm-color-primary);
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: var(--ifm-color-primary-dark);
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--ifm-heading-color);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
}

h3 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

p {
  line-height: 1.7;
  color: var(--ifm-color-content);
}

/* Modern List Styling */
ul, ol {
  line-height: 1.7;
}

li {
  margin-bottom: 0.5rem;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== EARNINGS CALCULATOR COMPONENTS ===== */

/* Modern Earnings Calculator Link */
.navbar__link--earnings {
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-info) 100%) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  margin-left: 0.5rem !important;
  box-shadow: var(--ifm-box-shadow-sm) !important;
}

.navbar__link--earnings:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--ifm-box-shadow-md) !important;
  color: white !important;
  background: linear-gradient(135deg, var(--ifm-color-primary-dark) 0%, var(--ifm-color-info) 100%) !important;
}

[data-theme='dark'] .navbar__link--earnings {
  background: linear-gradient(135deg, var(--ifm-color-primary-light) 0%, var(--ifm-color-info) 100%) !important;
}

/* Earnings Calculator Button Component */
.earnings-calculator-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-info) 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  box-shadow: var(--ifm-box-shadow-md);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.earnings-calculator-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--ifm-box-shadow-lg);
  color: white;
  text-decoration: none;
  background: linear-gradient(135deg, var(--ifm-color-primary-dark) 0%, var(--ifm-color-info) 100%);
}

/* Earnings Calculator Feature Box */
.earnings-calculator-box {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05) 0%,
    rgba(59, 130, 246, 0.05) 50%,
    rgba(139, 92, 246, 0.05) 100%);
  border: 2px solid var(--ifm-color-primary);
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  box-shadow: var(--ifm-box-shadow-md);
  transition: all 0.2s ease;
}

.earnings-calculator-box:hover {
  transform: translateY(-2px);
  box-shadow: var(--ifm-box-shadow-lg);
}

.earnings-calculator-box h3 {
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-info) 50%, var(--ifm-color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.earnings-calculator-box p {
  color: var(--ifm-color-content-secondary);
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
}

[data-theme='dark'] .earnings-calculator-box {
  background: linear-gradient(135deg,
    rgba(129, 140, 248, 0.1) 0%,
    rgba(96, 165, 250, 0.1) 50%,
    rgba(167, 139, 250, 0.1) 100%);
  border-color: var(--ifm-color-primary-light);
}

/* ===== FINAL MODERN TOUCHES ===== */

/* Smooth Animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Modern Footer */
.footer {
  background: var(--ifm-color-emphasis-100);
  border-top: 1px solid var(--ifm-border-color);
  padding: 3rem 0 2rem;
}

.footer__title {
  font-weight: 600;
  color: var(--ifm-heading-color);
  margin-bottom: 1rem;
}

.footer__item {
  color: var(--ifm-color-content-secondary);
  transition: all 0.2s ease;
}

.footer__item:hover {
  color: var(--ifm-color-primary);
}

/* Modern Search */
.navbar__search {
  margin-right: 1rem;
}

.DocSearch-Button {
  border-radius: 8px !important;
  border: 1px solid var(--ifm-border-color) !important;
  background: var(--ifm-background-surface-color) !important;
  transition: all 0.2s ease !important;
}

.DocSearch-Button:hover {
  border-color: var(--ifm-color-primary) !important;
  box-shadow: var(--ifm-box-shadow-sm) !important;
}

/* ===== MODERN COMPONENTS ===== */

/* Modern Code Block */
.modern-code-block {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.code-block-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--ifm-color-emphasis-100);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--ifm-border-color);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  font-weight: 600;
}

.code-block-title__text {
  color: var(--ifm-color-content-secondary);
}

.code-block-copy-button {
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-block-copy-button:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-primary);
}

/* Interactive Demo */
.interactive-demo {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 2rem 0;
  background: var(--ifm-background-color);
}

.interactive-demo__header {
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.interactive-demo__header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

.interactive-demo__header p {
  margin: 0;
  color: var(--ifm-color-content-secondary);
}

.interactive-demo__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.interactive-demo__tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.interactive-demo__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.interactive-demo__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.interactive-demo__example {
  padding: 1.5rem;
}

.interactive-demo__code {
  position: relative;
  background: var(--ifm-code-background);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.interactive-demo__code pre {
  margin: 0;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
}

.interactive-demo__run-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--ifm-color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.interactive-demo__run-button:hover:not(:disabled) {
  background: var(--ifm-color-primary-dark);
  transform: translateY(-1px);
}

.interactive-demo__run-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.interactive-demo__result {
  background: var(--ifm-color-emphasis-0);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--ifm-border-color);
}

.interactive-demo__result h4 {
  margin: 0 0 1rem 0;
  color: var(--ifm-heading-color);
}

.interactive-demo__success pre {
  background: var(--ifm-code-background);
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  margin: 0;
}

.interactive-demo__error {
  color: var(--ifm-color-danger);
  background: rgba(239, 68, 68, 0.1);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Feature Comparison */
.feature-comparison {
  margin: 2rem 0;
}

.feature-comparison h3 {
  margin-bottom: 1.5rem;
  color: var(--ifm-heading-color);
}

/* ===== PROFESSIONAL COMPONENTS ===== */

/* API Reference */
.api-reference {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 2rem 0;
  background: var(--ifm-background-color);
}

.api-reference__header {
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-reference__endpoint {
  margin-bottom: 1rem;
}

.api-reference__description {
  margin: 0;
  color: var(--ifm-color-content-secondary);
  line-height: 1.6;
}

.api-reference__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-reference__tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.api-reference__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.api-reference__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.api-reference__tab-content {
  padding: 1.5rem;
}

.api-response {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
}

.api-response__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-response__description {
  color: var(--ifm-color-content-secondary);
}

.api-response__schema {
  margin: 0;
  padding: 1rem;
  background: var(--ifm-code-background);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  overflow-x: auto;
}

.api-example {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--ifm-border-color);
  background: var(--ifm-color-emphasis-0);
}

.api-example h4 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

.api-example p {
  margin: 0 0 1rem 0;
  color: var(--ifm-color-content-secondary);
}

.api-example pre {
  background: var(--ifm-code-background);
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  margin: 1rem 0;
}

.api-example__response {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-border-color);
}

.api-example__response h5 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

/* Feature Callout */
.feature-callout {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  padding: 1.5rem;
  margin: 1.5rem 0;
  background: var(--ifm-background-color);
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.feature-callout:hover {
  box-shadow: var(--ifm-box-shadow-md);
  transform: translateY(-1px);
}

.feature-callout__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-callout__icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--ifm-color-emphasis-100);
}

.feature-callout__title {
  margin: 0;
  color: var(--ifm-heading-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.feature-callout__content {
  color: var(--ifm-color-content);
  line-height: 1.6;
}

.feature-callout__action {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-border-color);
}

/* Feature Callout Types */
.feature-callout--primary {
  border-left: 4px solid var(--ifm-color-primary);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(99, 102, 241, 0.02) 100%);
}

.feature-callout--primary .feature-callout__icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--ifm-color-primary);
}

.feature-callout--success {
  border-left: 4px solid var(--ifm-color-success);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.feature-callout--success .feature-callout__icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--ifm-color-success);
}

.feature-callout--warning {
  border-left: 4px solid var(--ifm-color-warning);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.feature-callout--warning .feature-callout__icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--ifm-color-warning);
}

.feature-callout--danger {
  border-left: 4px solid var(--ifm-color-danger);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.feature-callout--danger .feature-callout__icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--ifm-color-danger);
}

.feature-callout--info {
  border-left: 4px solid var(--ifm-color-info);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
}

.feature-callout--info .feature-callout__icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--ifm-color-info);
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.status-indicator--small {
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  gap: 0.25rem;
}

.status-indicator--large {
  font-size: 1rem;
  padding: 0.5rem 1rem;
  gap: 0.75rem;
}

.status-indicator__icon {
  font-size: 0.875em;
}

.status-indicator__label {
  font-weight: 600;
}

/* Status Types */
.status-indicator--available {
  color: var(--ifm-color-success);
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.status-indicator--coming-soon {
  color: var(--ifm-color-warning);
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.status-indicator--not-available {
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-color-emphasis-100);
  border-color: var(--ifm-color-emphasis-200);
}

.status-indicator--beta {
  color: var(--ifm-color-info);
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.status-indicator--deprecated {
  color: var(--ifm-color-danger);
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-indicator--unknown {
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-color-emphasis-100);
  border-color: var(--ifm-color-emphasis-200);
}

/* ===== FINAL RESPONSIVE TOUCHES ===== */

/* Mobile optimizations for new components */
@media (max-width: 768px) {
  .api-reference__tabs {
    flex-direction: column;
  }

  .api-reference__tab {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--ifm-border-color);
    border-right: none;
  }

  .api-reference__tab--active {
    border-bottom-color: var(--ifm-border-color);
    border-left: 3px solid var(--ifm-color-primary);
  }

  .feature-callout__header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .interactive-demo__run-button {
    position: static;
    margin-top: 1rem;
    width: 100%;
    justify-content: center;
  }

  .status-indicator {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
  }
}
